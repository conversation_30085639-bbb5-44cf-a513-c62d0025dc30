from data_provider.data_factory import data_provider
from exp.exp_basic import Exp_Basic
from utils.tools import EarlyStopping, adjust_learning_rate, visual
from utils.metrics import metric
import torch
import torch.nn as nn
from torch import optim
import os
import time
import warnings
import numpy as np

warnings.filterwarnings('ignore')


class Exp_Long_Term_Forecast(Exp_Basic):
    def __init__(self, args):
        super(Exp_Long_Term_Forecast, self).__init__(args)

    def _build_model(self):
        model = self.model_dict[self.args.model].Model(self.args).float()

        if self.args.use_multi_gpu and self.args.use_gpu:
            model = nn.DataParallel(model, device_ids=self.args.device_ids)
        return model

    def _get_data(self, flag):
        data_set, data_loader = data_provider(self.args, flag)
        return data_set, data_loader

    def _select_optimizer(self):
        model_optim = optim.Adam(self.model.parameters(), lr=self.args.learning_rate)
        return model_optim

    def _select_criterion(self):
        criterion = nn.MSELoss()
        return criterion

    def vali(self, vali_data, vali_loader, criterion):
        total_loss = []
        self.model.eval()
        with torch.no_grad():
            for i, (batch_x, batch_y, batch_x_mark, batch_y_mark) in enumerate(vali_loader):
                batch_x = batch_x.float().to(self.device)
                batch_y = batch_y.float()

                batch_x_mark = batch_x_mark.float().to(self.device)
                batch_y_mark = batch_y_mark.float().to(self.device)

                # decoder input
                dec_inp = torch.zeros_like(batch_y[:, -self.args.pred_len:, :]).float()
                dec_inp = torch.cat([batch_y[:, :self.args.label_len, :], dec_inp], dim=1).float().to(self.device)
                # encoder - decoder
                if self.args.use_amp:
                    with torch.cuda.amp.autocast():
                        if self.args.output_attention:
                            outputs = self.model(batch_x, batch_x_mark, dec_inp, batch_y_mark)[0]
                        else:
                            outputs = self.model(batch_x, batch_x_mark, dec_inp, batch_y_mark)
                else:
                    if self.args.output_attention:
                        outputs = self.model(batch_x, batch_x_mark, dec_inp, batch_y_mark)[0]
                    else:
                        outputs = self.model(batch_x, batch_x_mark, dec_inp, batch_y_mark)
                f_dim = -1 if self.args.features == 'MS' else 0
                outputs = outputs[:, -self.args.pred_len:, f_dim:]
                batch_y = batch_y[:, -self.args.pred_len:, f_dim:].to(self.device)

                pred = outputs.detach().cpu()
                true = batch_y.detach().cpu()

                loss = criterion(pred, true)

                total_loss.append(loss)
        total_loss = np.average(total_loss)
        self.model.train()
        return total_loss

    def train(self, setting):
        train_data, train_loader = self._get_data(flag='train')
        vali_data, vali_loader = self._get_data(flag='val')
        test_data, test_loader = self._get_data(flag='test')

        path = os.path.join(self.args.checkpoints, setting)
        if not os.path.exists(path):
            os.makedirs(path)

        time_now = time.time()

        train_steps = len(train_loader)
        early_stopping = EarlyStopping(patience=self.args.patience, verbose=True)

        model_optim = self._select_optimizer()
        criterion = self._select_criterion()

        if self.args.use_amp:
            scaler = torch.cuda.amp.GradScaler()

        for epoch in range(self.args.train_epochs):
            iter_count = 0
            train_loss = []

            self.model.train()
            epoch_time = time.time()
            for i, (batch_x, batch_y, batch_x_mark, batch_y_mark) in enumerate(train_loader):
                iter_count += 1
                model_optim.zero_grad()
                batch_x = batch_x.float().to(self.device)

                batch_y = batch_y.float().to(self.device)
                batch_x_mark = batch_x_mark.float().to(self.device)
                batch_y_mark = batch_y_mark.float().to(self.device)

                # decoder input
                dec_inp = torch.zeros_like(batch_y[:, -self.args.pred_len:, :]).float()
                dec_inp = torch.cat([batch_y[:, :self.args.label_len, :], dec_inp], dim=1).float().to(self.device)

                # encoder - decoder
                if self.args.use_amp:
                    with torch.cuda.amp.autocast():
                        if self.args.output_attention:
                            outputs = self.model(batch_x, batch_x_mark, dec_inp, batch_y_mark)[0]
                        else:
                            outputs = self.model(batch_x, batch_x_mark, dec_inp, batch_y_mark)

                        f_dim = -1 if self.args.features == 'MS' else 0
                        outputs = outputs[:, -self.args.pred_len:, f_dim:]
                        batch_y = batch_y[:, -self.args.pred_len:, f_dim:].to(self.device)
                        loss = criterion(outputs, batch_y)
                        train_loss.append(loss.item())
                else:
                    if self.args.output_attention:
                        outputs = self.model(batch_x, batch_x_mark, dec_inp, batch_y_mark)[0]
                    else:
                        outputs = self.model(batch_x, batch_x_mark, dec_inp, batch_y_mark)

                    f_dim = -1 if self.args.features == 'MS' else 0
                    outputs = outputs[:, -self.args.pred_len:, f_dim:]
                    batch_y = batch_y[:, -self.args.pred_len:, f_dim:].to(self.device)
                    loss = criterion(outputs, batch_y)
                    train_loss.append(loss.item())

                if (i + 1) % 100 == 0:
                    print("\titers: {0}, epoch: {1} | loss: {2:.7f}".format(i + 1, epoch + 1, loss.item()))
                    speed = (time.time() - time_now) / iter_count
                    left_time = speed * ((self.args.train_epochs - epoch) * train_steps - i)
                    print('\tspeed: {:.4f}s/iter; left time: {:.4f}s'.format(speed, left_time))
                    iter_count = 0
                    time_now = time.time()

                if self.args.use_amp:
                    scaler.scale(loss).backward()
                    scaler.step(model_optim)
                    scaler.update()
                else:
                    loss.backward()
                    model_optim.step()

            print("Epoch: {} cost time: {}".format(epoch + 1, time.time() - epoch_time))
            train_loss = np.average(train_loss)
            vali_loss = self.vali(vali_data, vali_loader, criterion)
            test_loss = self.vali(test_data, test_loader, criterion)

            print("Epoch: {0}, Steps: {1} | Train Loss: {2:.7f} Vali Loss: {3:.7f} Test Loss: {4:.7f}".format(
                epoch + 1, train_steps, train_loss, vali_loss, test_loss))
            early_stopping(vali_loss, self.model, path)
            if early_stopping.early_stop:
                print("Early stopping")
                break

            adjust_learning_rate(model_optim, epoch + 1, self.args)

        best_model_path = path + '/' + 'checkpoint.pth'
        self.model.load_state_dict(torch.load(best_model_path))

        return self.model

    def test(self, setting, test=0):
        test_data, test_loader = self._get_data(flag='test')
        if test:
            print('loading model')
            self.model.load_state_dict(torch.load(os.path.join(self.args.checkpoints, setting, 'checkpoint.pth')))

        preds = []
        trues = []
        inputs = []  # 存储历史输入数据用于详细结果
        test_results_dir = os.path.join(os.path.dirname(self.args.checkpoints), 'test_results')
        folder_path = os.path.join(test_results_dir, setting) + '/'
        if not os.path.exists(folder_path):
            os.makedirs(folder_path)

        self.model.eval()
        with torch.no_grad():
            for i, (batch_x, batch_y, batch_x_mark, batch_y_mark) in enumerate(test_loader):
                batch_x = batch_x.float().to(self.device)
                batch_y = batch_y.float().to(self.device)

                batch_x_mark = batch_x_mark.float().to(self.device)
                batch_y_mark = batch_y_mark.float().to(self.device)

                # decoder input
                dec_inp = torch.zeros_like(batch_y[:, -self.args.pred_len:, :]).float()
                dec_inp = torch.cat([batch_y[:, :self.args.label_len, :], dec_inp], dim=1).float().to(self.device)
                # encoder - decoder
                if self.args.use_amp:
                    with torch.cuda.amp.autocast():
                        if self.args.output_attention:
                            outputs = self.model(batch_x, batch_x_mark, dec_inp, batch_y_mark)[0]
                        else:
                            outputs = self.model(batch_x, batch_x_mark, dec_inp, batch_y_mark)
                else:
                    if self.args.output_attention:
                        outputs = self.model(batch_x, batch_x_mark, dec_inp, batch_y_mark)[0]

                    else:
                        outputs = self.model(batch_x, batch_x_mark, dec_inp, batch_y_mark)

                f_dim = -1 if self.args.features == 'MS' else 0
                outputs = outputs[:, -self.args.pred_len:, :]
                batch_y = batch_y[:, -self.args.pred_len:, :].to(self.device)
                outputs = outputs.detach().cpu().numpy()
                batch_y = batch_y.detach().cpu().numpy()
                if test_data.scale and self.args.inverse:
                    shape = outputs.shape
                    outputs = test_data.inverse_transform(outputs.squeeze(0)).reshape(shape)
                    batch_y = test_data.inverse_transform(batch_y.squeeze(0)).reshape(shape)
        
                outputs = outputs[:, :, f_dim:]
                batch_y = batch_y[:, :, f_dim:]

                pred = outputs
                true = batch_y

                # 存储输入数据用于详细结果生成
                input_data = batch_x.detach().cpu().numpy()
                if test_data.scale and self.args.inverse:
                    shape = input_data.shape
                    input_data = test_data.inverse_transform(input_data.squeeze(0)).reshape(shape)
                inputs.append(input_data)

                preds.append(pred)
                trues.append(true)
                if i % 20 == 0:
                    input = batch_x.detach().cpu().numpy()
                    if test_data.scale and self.args.inverse:
                        shape = input.shape
                        input = test_data.inverse_transform(input.squeeze(0)).reshape(shape)
                    gt = np.concatenate((input[0, :, -1], true[0, :, -1]), axis=0)
                    pd = np.concatenate((input[0, :, -1], pred[0, :, -1]), axis=0)
                    visual(gt, pd, os.path.join(folder_path, str(i) + '.pdf'))

        preds = np.array(preds)
        trues = np.array(trues)
        inputs = np.array(inputs)
        print('test shape:', preds.shape, trues.shape)
        preds = preds.reshape(-1, preds.shape[-2], preds.shape[-1])
        trues = trues.reshape(-1, trues.shape[-2], trues.shape[-1])
        inputs = inputs.reshape(-1, inputs.shape[-2], inputs.shape[-1])
        print('test shape:', preds.shape, trues.shape)

        # result save
        results_dir = os.path.join(os.path.dirname(self.args.checkpoints), 'results')
        folder_path = os.path.join(results_dir, setting) + '/'
        if not os.path.exists(folder_path):
            os.makedirs(folder_path)

        mae, mse, rmse, mape, mspe = metric(preds, trues)
        print('mse:{}, mae:{}'.format(mse, mae))
        f = open("result_long_term_forecast.txt", 'a')
        f.write(setting + "  \n")
        f.write('mse:{}, mae:{}'.format(mse, mae))
        f.write('\n')
        f.write('\n')
        f.close()

        np.save(folder_path + 'metrics.npy', np.array([mae, mse, rmse, mape, mspe]))
        np.save(folder_path + 'pred.npy', preds)
        np.save(folder_path + 'true.npy', trues)

        # 生成详细的预测结果文件
        self._generate_detailed_results(folder_path, inputs, preds, trues, mae, mse, rmse, mape, mspe)

        return

    def _generate_detailed_results(self, folder_path, inputs, preds, trues, mae, mse, rmse, mape, mspe):
        """生成详细的预测结果文件"""
        result_file = os.path.join(folder_path, 'prediction_results.txt')

        with open(result_file, 'w', encoding='utf-8') as f:
            # 写入整体评估指标
            f.write("整体评估指标:\n")
            f.write("-" * 50 + "\n")
            f.write(f"MSE: {mse:.10f}\n")
            f.write(f"MAE: {mae:.10f}\n")
            f.write(f"RMSE: {rmse:.10f}\n")
            f.write(f"MAPE: {mape:.10f}\n")
            f.write(f"MSPE: {mspe:.10f}\n")
            f.write("-" * 50 + "\n\n")

            # 选择几个样本进行详细展示（每20个样本展示一个）
            sample_indices = list(range(0, len(preds), 20))

            for idx, sample_idx in enumerate(sample_indices):
                if sample_idx >= len(preds):
                    break

                f.write(f"图像 {sample_idx}.png 对应的预测值与真实值对比:\n")

                # 写入历史数据
                f.write("历史数据:\n")
                f.write("时间步\t数值\n")
                f.write("-" * 30 + "\n")

                # 获取历史数据（输入序列的最后一个特征）
                hist_data = inputs[sample_idx][:, -1]  # 取最后一个特征
                for i, val in enumerate(hist_data):
                    f.write(f"{i+1}\t{val:.4f}\n")

                f.write("\n预测结果对比:\n")
                f.write("时间步\t真实值\t\t预测值\t\t误差\n")
                f.write("-" * 50 + "\n")

                # 写入预测对比
                pred_data = preds[sample_idx][:, -1] if preds[sample_idx].shape[-1] > 1 else preds[sample_idx][:, 0]
                true_data = trues[sample_idx][:, -1] if trues[sample_idx].shape[-1] > 1 else trues[sample_idx][:, 0]

                for i in range(len(pred_data)):
                    true_val = true_data[i]
                    pred_val = pred_data[i]
                    error = abs(true_val - pred_val)
                    time_step = len(hist_data) + i + 1
                    f.write(f"{time_step}\t{true_val:.4f}\t\t{pred_val:.4f}\t\t{error:.4f}\n")

                f.write("\n")

        print(f"详细预测结果已保存到: {result_file}")

    def predict_api(self, setting, csv_path, pred_len):
        """
        专门用于API预测的方法
        使用所有历史数据预测未来pred_len个时间点
        """
        import pandas as pd
        import numpy as np
        from sklearn.preprocessing import StandardScaler
        from utils.timefeatures import time_features
        import torch
        from torch.utils.data import Dataset, DataLoader

        # 加载模型
        print('loading model for API prediction')
        self.model.load_state_dict(torch.load(os.path.join(self.args.checkpoints, setting, 'checkpoint.pth')))

        # 读取数据
        df = pd.read_csv(csv_path)

        # 数据预处理
        scaler = StandardScaler()
        data = df[['vol']].values
        scaled_data = scaler.fit_transform(data)

        # 时间特征
        df['date'] = pd.to_datetime(df['date'])
        if self.args.timeenc == 0:
            # 对于日频率，使用3个时间特征：DayOfWeek, DayOfMonth, DayOfYear
            df['dayofweek'] = df['date'].dt.dayofweek / 6.0 - 0.5  # [0,6] -> [-0.5, 0.5]
            df['dayofmonth'] = (df['date'].dt.day - 1) / 30.0 - 0.5  # [1,31] -> [-0.5, 0.5]
            df['dayofyear'] = (df['date'].dt.dayofyear - 1) / 365.0 - 0.5  # [1,366] -> [-0.5, 0.5]
            time_features_data = df[['dayofweek', 'dayofmonth', 'dayofyear']].values
        else:
            time_features_data = time_features(df['date'].values, freq=self.args.freq)
            time_features_data = time_features_data.transpose(1, 0)

        # 使用最后seq_len个数据点进行预测
        seq_len = self.args.seq_len
        if len(scaled_data) < seq_len:
            # 如果数据不够，用最后的数据重复填充
            repeat_times = (seq_len // len(scaled_data)) + 1
            scaled_data = np.tile(scaled_data, (repeat_times, 1))[:seq_len]
            time_features_data = np.tile(time_features_data, (repeat_times, 1))[:seq_len]

        # 取最后seq_len个点作为输入
        input_data = scaled_data[-seq_len:]
        input_time_features = time_features_data[-seq_len:]

        # 构造输入张量
        seq_x = torch.FloatTensor(input_data).unsqueeze(0).to(self.device)  # [1, seq_len, 1]
        seq_x_mark = torch.FloatTensor(input_time_features).unsqueeze(0).to(self.device)  # [1, seq_len, time_features]

        # 构造decoder输入
        label_len = self.args.label_len
        pred_len = self.args.pred_len

        # decoder输入：label部分来自历史数据，pred部分用零填充
        dec_inp = torch.zeros(1, label_len + pred_len, 1).to(self.device)
        dec_inp[:, :label_len, :] = seq_x[:, -label_len:, :]

        # decoder时间特征
        # 为未来时间点生成时间特征
        last_date = df['date'].iloc[-1]
        future_dates = pd.date_range(start=last_date, periods=pred_len + 1, freq='D')[1:]  # 排除起始日期

        if self.args.timeenc == 0:
            # 对于日频率，使用3个时间特征：DayOfWeek, DayOfMonth, DayOfYear
            future_time_features = np.array([
                [
                    date.dayofweek / 6.0 - 0.5,  # DayOfWeek
                    (date.day - 1) / 30.0 - 0.5,  # DayOfMonth
                    (date.dayofyear - 1) / 365.0 - 0.5  # DayOfYear
                ] for date in future_dates
            ])
        else:
            future_time_features = time_features(future_dates.values, freq=self.args.freq)
            future_time_features = future_time_features.transpose(1, 0)

        # 组合decoder时间特征
        dec_inp_mark = torch.zeros(1, label_len + pred_len, time_features_data.shape[1]).to(self.device)
        dec_inp_mark[:, :label_len, :] = seq_x_mark[:, -label_len:, :]
        dec_inp_mark[:, label_len:, :] = torch.FloatTensor(future_time_features).unsqueeze(0).to(self.device)

        # 预测
        self.model.eval()
        with torch.no_grad():
            if self.args.output_attention:
                outputs = self.model(seq_x, seq_x_mark, dec_inp, dec_inp_mark)[0]
            else:
                outputs = self.model(seq_x, seq_x_mark, dec_inp, dec_inp_mark)

        # 提取预测结果
        prediction = outputs[:, -pred_len:, :].detach().cpu().numpy()  # [1, pred_len, 1]

        # 反归一化
        if self.args.inverse:
            prediction = scaler.inverse_transform(prediction.reshape(-1, 1)).reshape(prediction.shape)

        # 保存结果
        results_dir = os.path.join(os.path.dirname(self.args.checkpoints), 'results')
        folder_path = os.path.join(results_dir, setting)
        if not os.path.exists(folder_path):
            os.makedirs(folder_path)

        np.save(os.path.join(folder_path, 'pred.npy'), prediction)

        print(f'API预测完成，预测形状: {prediction.shape}')
        return prediction
