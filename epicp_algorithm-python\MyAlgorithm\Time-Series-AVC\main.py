import subprocess
import sys

def run_autoformer():
    args = [
        sys.executable,  # 使用当前Python解释器
        "-u",  # 无缓冲输出
        "run.py",
        "--is_training", "0",
        "--root_path", "./dataset",
        "--data_path", "voltage.csv",
        "--model_id", "Informer  0.002",
        "--model", "Informer",
        "--data", "custom",
        "--freq", "d",
        "--features", "S",
        "--target", "vol",  # 添加目标列名参数
        "--seq_len", "28",
        "--label_len", "14",
        "--pred_len", "7",
        "--e_layers", "2",
        "--d_layers", "1",
        "--factor", "3",
        "--enc_in", "1",
        "--dec_in", "1",
        "--c_out", "1",
        "--learning_rate", "0.002",
        "--d_model", "64",
        "--n_heads", "8",
        "--train_epochs", "50",
        "--batch_size", "16",
        "--patience", "10",
        "--des", "voltage_forecast",
        "--itr", "1",
        "--dropout", "0.2",
        "--inverse"
    ]
    
    try:
        # 显示执行命令（调试用）
        print("Executing:", ' '.join(args))
        
        # 启动子进程
        process = subprocess.Popen(
            args,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True
        )
        
        # 实时输出日志
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                print(output.strip())
                
        # 检查返回码
        return_code = process.poll()
        if return_code != 0:
            print(f"Process exited with code {return_code}")
            
    except Exception as e:
        print(f"执行失败: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    run_autoformer()