#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门为API调用设计的时间序列预测接口
"""

import argparse
import os
import torch
import pandas as pd
import numpy as np
import json
from datetime import datetime, timedelta
from exp.exp_long_term_forecasting import Exp_Long_Term_Forecast
from utils.print_args import print_args
import random

def setup_args(data_path, output_path=None, pred_len=7, seq_len=28):
    """设置预测参数"""
    args = argparse.Namespace()
    
    # 基本配置
    args.task_name = 'long_term_forecast'
    args.is_training = 0  # 预测模式
    args.model_id = 'voltage_predictor_api'
    args.model = 'Informer'
    
    # 数据配置
    args.data = 'custom'
    args.root_path = './dataset'
    args.data_path = data_path
    args.features = 'S'  # 单变量预测
    args.target = 'vol'
    args.freq = 'd'  # 日频率
    args.checkpoints = './checkpoints'
    
    # 预测参数
    args.seq_len = seq_len      # 输入序列长度
    args.label_len = seq_len // 2  # 标签长度
    args.pred_len = pred_len    # 预测长度
    args.inverse = True         # 反归一化
    
    # 模型参数
    args.enc_in = 1
    args.dec_in = 1
    args.c_out = 1
    args.d_model = 64
    args.n_heads = 8
    args.e_layers = 2
    args.d_layers = 1
    args.d_ff = 2048
    args.factor = 3
    args.dropout = 0.2
    args.embed = 'timeF'
    args.activation = 'gelu'
    args.output_attention = False
    args.distil = True
    args.moving_avg = 25
    
    # 训练参数
    args.num_workers = 0
    args.itr = 1  # 添加缺失的itr参数
    args.train_epochs = 10
    args.batch_size = 16
    args.patience = 3
    args.learning_rate = 0.002
    args.des = 'voltage_forecast_api'
    args.loss = 'MSE'
    args.lradj = 'type1'
    args.use_amp = False
    
    # GPU配置
    args.use_gpu = torch.cuda.is_available()
    args.gpu = 0
    args.use_multi_gpu = False
    args.devices = '0'
    
    # 其他参数
    args.seasonal_patterns = 'Monthly'
    args.top_k = 5
    args.num_kernels = 6
    args.p_hidden_dims = [128, 128]
    args.p_hidden_layers = 2
    args.do_predict = False

    # 添加缺失的参数
    args.mask_rate = 0.25
    args.anomaly_ratio = 0.25
    
    # 输出路径
    args.output_path = output_path or './api_results'
    
    return args

def prepare_data_from_json(json_data, output_csv_path):
    """
    从JSON数据准备CSV文件
    
    Args:
        json_data: 时间序列数据，格式如 [{"time":"2025-04-24 00:00:00","value":111}, ...]
        output_csv_path: 输出CSV文件路径
    
    Returns:
        bool: 是否成功准备数据
    """
    try:
        # 解析JSON数据
        if isinstance(json_data, str):
            data_list = json.loads(json_data)
        else:
            data_list = json_data
        
        # 提取时间和数值
        dates = []
        values = []
        
        for item in data_list:
            # 支持多种时间字段名
            time_value = None
            if 'time' in item:
                time_value = item['time']
            elif 'date' in item:
                time_value = item['date']
            elif 'timestamp' in item:
                time_value = item['timestamp']
            
            # 支持多种数值字段名
            vol_value = None
            if 'value' in item:
                vol_value = float(item['value'])
            elif 'vol' in item:
                vol_value = float(item['vol'])
            elif 'voltage' in item:
                vol_value = float(item['voltage'])
            elif '电压' in item:
                vol_value = float(item['电压'])
            
            if time_value and vol_value is not None:
                # 解析时间
                try:
                    if isinstance(time_value, str):
                        # 尝试多种时间格式
                        for fmt in ['%Y-%m-%d %H:%M:%S', '%Y-%m-%d', '%Y/%m/%d', '%Y-%m-%d %H:%M']:
                            try:
                                parsed_time = datetime.strptime(time_value, fmt)
                                break
                            except ValueError:
                                continue
                        else:
                            # 如果都不匹配，尝试pandas的自动解析
                            parsed_time = pd.to_datetime(time_value)
                    else:
                        parsed_time = pd.to_datetime(time_value)
                    
                    dates.append(parsed_time.strftime('%Y/%m/%d'))
                    values.append(vol_value)
                except Exception as e:
                    print(f"时间解析失败: {time_value}, 错误: {e}")
                    continue
        
        if len(dates) == 0:
            print("未找到有效的时间序列数据")
            return False
        
        # 创建DataFrame
        df = pd.DataFrame({
            'date': dates,
            'vol': values
        })
        
        # 按日期排序
        df['date_parsed'] = pd.to_datetime(df['date'])
        df = df.sort_values('date_parsed')
        df = df.drop('date_parsed', axis=1)
        
        # 保存CSV文件
        os.makedirs(os.path.dirname(output_csv_path), exist_ok=True)
        df.to_csv(output_csv_path, index=False)
        
        print(f"成功准备数据: {len(df)} 条记录，保存到 {output_csv_path}")
        return True
        
    except Exception as e:
        print(f"数据准备失败: {e}")
        return False

def run_prediction_api(json_data, pred_len=7, seq_len=28, output_path=None):
    """
    API预测接口
    
    Args:
        json_data: 输入的时间序列数据
        pred_len: 预测长度（天数）
        seq_len: 输入序列长度
        output_path: 输出路径
    
    Returns:
        dict: 预测结果
    """
    try:
        # 设置随机种子
        fix_seed = 2021
        random.seed(fix_seed)
        torch.manual_seed(fix_seed)
        np.random.seed(fix_seed)
        
        # 准备临时数据文件
        temp_csv_path = os.path.join('./dataset', 'temp_api_data.csv')
        
        # 从JSON准备CSV数据
        if not prepare_data_from_json(json_data, temp_csv_path):
            return {"error": "数据准备失败"}
        
        # 设置参数
        args = setup_args(
            data_path='temp_api_data.csv',
            output_path=output_path,
            pred_len=pred_len,
            seq_len=seq_len
        )
        
        print('API预测参数:')
        print_args(args)
        
        # 创建实验对象
        exp = Exp_Long_Term_Forecast(args)
        
        # 构建设置字符串
        setting = '{}_{}_{}_{}_ft{}_sl{}_ll{}_pl{}_dm{}_nh{}_el{}_dl{}_df{}_fc{}_eb{}_dt{}_{}_0'.format(
            args.task_name,
            args.model_id,
            args.model,
            args.data,
            args.features,
            args.seq_len,
            args.label_len,
            args.pred_len,
            args.d_model,
            args.n_heads,
            args.e_layers,
            args.d_layers,
            args.d_ff,
            args.factor,
            args.embed,
            args.distil,
            args.des
        )
        
        # 检查是否存在预训练模型
        model_path = os.path.join(args.checkpoints, setting, 'checkpoint.pth')
        if not os.path.exists(model_path):
            print("未找到预训练模型，使用简单预测方法")
            return simple_prediction_fallback(temp_csv_path, pred_len)
        
        print(f'开始预测，设置: {setting}')
        
        # 执行预测
        exp.test(setting, test=1)
        
        # 读取预测结果
        result_path = os.path.join('./results', setting, 'pred.npy')
        if os.path.exists(result_path):
            predictions = np.load(result_path)
            
            # 取最后一个预测序列
            if len(predictions.shape) > 2:
                last_prediction = predictions[-1, :, 0]
            else:
                last_prediction = predictions[-1]
            
            # 读取原始数据获取最后日期
            df = pd.read_csv(temp_csv_path)
            last_date = pd.to_datetime(df['date'].iloc[-1])
            
            # 生成预测日期
            pred_dates = pd.date_range(
                start=last_date + timedelta(days=1), 
                periods=len(last_prediction), 
                freq='D'
            )
            
            # 构建结果
            result = {
                "success": True,
                "predictions": [
                    {
                        "date": date.strftime('%Y-%m-%d'),
                        "predicted_voltage": float(voltage)
                    }
                    for date, voltage in zip(pred_dates, last_prediction)
                ],
                "model_info": {
                    "model_type": "Informer",
                    "sequence_length": args.seq_len,
                    "prediction_length": args.pred_len,
                    "input_data_points": len(df)
                },
                "input_summary": {
                    "start_date": df['date'].iloc[0],
                    "end_date": df['date'].iloc[-1],
                    "data_points": len(df),
                    "voltage_range": [float(df['vol'].min()), float(df['vol'].max())]
                }
            }
            
            # 清理临时文件
            if os.path.exists(temp_csv_path):
                os.remove(temp_csv_path)
            
            return result
        else:
            print("未找到预测结果文件，使用简单预测方法")
            return simple_prediction_fallback(temp_csv_path, pred_len)
            
    except Exception as e:
        print(f"预测执行失败: {e}")
        import traceback
        traceback.print_exc()
        return {"error": f"预测失败: {str(e)}"}

def simple_prediction_fallback(csv_path, pred_len):
    """简单预测回退方法"""
    try:
        df = pd.read_csv(csv_path)
        voltage_values = df['vol'].values
        
        # 使用移动平均和趋势分析
        window_size = min(7, len(voltage_values))
        recent_avg = np.mean(voltage_values[-window_size:])
        
        # 计算趋势
        if len(voltage_values) >= 2:
            trend = (voltage_values[-1] - voltage_values[-min(7, len(voltage_values))]) / min(7, len(voltage_values))
        else:
            trend = 0
        
        # 生成预测值
        predictions = []
        for i in range(pred_len):
            pred_value = recent_avg + trend * (i + 1) + np.random.normal(0, 0.01)
            predictions.append(pred_value)
        
        # 生成预测日期
        last_date = pd.to_datetime(df['date'].iloc[-1])
        pred_dates = pd.date_range(start=last_date + timedelta(days=1), periods=pred_len, freq='D')
        
        result = {
            "success": True,
            "predictions": [
                {
                    "date": date.strftime('%Y-%m-%d'),
                    "predicted_voltage": float(voltage)
                }
                for date, voltage in zip(pred_dates, predictions)
            ],
            "model_info": {
                "model_type": "Simple Moving Average (Fallback)",
                "sequence_length": len(voltage_values),
                "prediction_length": pred_len
            },
            "note": "使用简单预测方法，建议训练专用模型以获得更好效果"
        }
        
        return result
        
    except Exception as e:
        return {"error": f"简单预测失败: {str(e)}"}

if __name__ == "__main__":
    # 测试用例
    test_data = [
        {"time": "2024-01-01", "value": 10.3},
        {"time": "2024-01-02", "value": 10.35},
        {"time": "2024-01-03", "value": 10.28},
        {"time": "2024-01-04", "value": 10.42},
        {"time": "2024-01-05", "value": 10.38}
    ]
    
    result = run_prediction_api(test_data, pred_len=7)
    print("预测结果:")
    print(json.dumps(result, ensure_ascii=False, indent=2))
