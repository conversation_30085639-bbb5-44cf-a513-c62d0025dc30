# 一、环境搭建
### 1.1 虚拟环境（python 3.12）
1. 创建虚拟环境：
``` shell
# venv 是python自带的虚拟环境管理工具
# .env 是用户自定义的虚拟环境名称
python -m venv .env
``` 
2. 启动虚拟环境
``` shell
# windows环境
.env/Scripts/activate
# macOS/Linux:
source .env/bin/activate
```
### 1.2 安装依赖（先激活虚拟环境）
``` shell
pip install -r requirements.txt
```
# 二、GRPC服务创建或更新（先激活虚拟环境）
### 先安装PROTOBUF (https://github.com/protocolbuffers/protobuf/releases)
### **<span style="color:red">`services`文件夹下代码是自动生成的,请不要手动修改</span>**
``` shell
#自动生成GRPC代码的指令
python -m grpc_tools.protoc -I=services/protos  --python_out=./services --grpc_python_out=./services ./services/protos/algorithm.proto ./services/protos/topologic.proto --mypy_out=readable_stubs:./services
# -I=services/protos                      protos文件路径
# --python_out=./services                 生成python service代码的路径
# --mypy_out=readable_stubs:./services    生成python service代码中函数的类型标注文件（.pyi）的路径
# --grpc_python_out=./services/grpc       生成python grpc代码的路径
# ./services/protos/algorithm.proto       需要编译的.proto文件
# ./services/protos/topologic.proto       需要编译的.proto文件

