from concurrent import futures
import grpc
from src.algorithm import MyAlgorithm
from services.algorithm_pb2_grpc import add_AlgorithmServicer_to_server


def serve():
    server = grpc.server(futures.ThreadPoolExecutor(max_workers=10))
    add_AlgorithmServicer_to_server(MyAlgorithm(),server)
    server.add_insecure_port('[::]:50051')
    server.start()
    print("Server started on port 50051")
    server.wait_for_termination()

if __name__ == '__main__':
    serve()