# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: algorithm.proto
# Protobuf Python Version: 5.29.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    0,
    '',
    'algorithm.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from . import topologic_pb2 as topologic__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0f\x61lgorithm.proto\x12\talgorithm\x1a\x0ftopologic.proto\"G\n\nInitParams\x12\n\n\x02id\x18\x01 \x01(\t\x12\x1d\n\x04topo\x18\x02 \x01(\x0b\x32\x0f.topologic.Topo\x12\x0e\n\x06option\x18\x03 \x01(\t\"*\n\nInitResult\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0b\n\x03msg\x18\x02 \x01(\t\"@\n\rRunStepParams\x12\n\n\x02id\x18\x01 \x01(\t\x12#\n\x05\x64\x61tas\x18\x02 \x03(\x0b\x32\x14.topologic.EquipData\"=\n\rRunStepResult\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0e\n\x06result\x18\x02 \x01(\t\x12\x0b\n\x03msg\x18\x03 \x01(\t\" \n\x12RunStepAfterParams\x12\n\n\x02id\x18\x01 \x01(\t\"!\n\x13RunStepBeforeParams\x12\n\n\x02id\x18\x01 \x01(\t\"\x07\n\x05\x45mpty\"\xb9\x01\n\rRunOnceParams\x12$\n\x06\x65quips\x18\x01 \x03(\x0b\x32\x14.topologic.Equipment\x12\x1e\n\x05\x65\x64ges\x18\x02 \x03(\x0b\x32\x0f.topologic.Edge\x12\x1e\n\x05nodes\x18\x03 \x03(\x0b\x32\x0f.topologic.Node\x12\"\n\x07\x62ranchs\x18\x04 \x03(\x0b\x32\x11.topologic.Branch\x12\r\n\x05\x64\x61tas\x18\x05 \x01(\t\x12\x0f\n\x07options\x18\x06 \x01(\t\">\n\rRunOnceResult\x12\x12\n\nis_success\x18\x01 \x01(\x08\x12\x0c\n\x04\x64\x61ta\x18\x02 \x01(\t\x12\x0b\n\x03msg\x18\x03 \x01(\t2\xc9\x02\n\tAlgorithm\x12\x34\n\x04init\x12\x15.algorithm.InitParams\x1a\x15.algorithm.InitResult\x12\x43\n\x0frun_step_before\x12\x1e.algorithm.RunStepBeforeParams\x1a\x10.algorithm.Empty\x12>\n\x08run_step\x12\x18.algorithm.RunStepParams\x1a\x18.algorithm.RunStepResult\x12\x41\n\x0erun_step_after\x12\x1d.algorithm.RunStepAfterParams\x1a\x10.algorithm.Empty\x12>\n\x08run_once\x12\x18.algorithm.RunOnceParams\x1a\x18.algorithm.RunOnceResultb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'algorithm_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_INITPARAMS']._serialized_start=47
  _globals['_INITPARAMS']._serialized_end=118
  _globals['_INITRESULT']._serialized_start=120
  _globals['_INITRESULT']._serialized_end=162
  _globals['_RUNSTEPPARAMS']._serialized_start=164
  _globals['_RUNSTEPPARAMS']._serialized_end=228
  _globals['_RUNSTEPRESULT']._serialized_start=230
  _globals['_RUNSTEPRESULT']._serialized_end=291
  _globals['_RUNSTEPAFTERPARAMS']._serialized_start=293
  _globals['_RUNSTEPAFTERPARAMS']._serialized_end=325
  _globals['_RUNSTEPBEFOREPARAMS']._serialized_start=327
  _globals['_RUNSTEPBEFOREPARAMS']._serialized_end=360
  _globals['_EMPTY']._serialized_start=362
  _globals['_EMPTY']._serialized_end=369
  _globals['_RUNONCEPARAMS']._serialized_start=372
  _globals['_RUNONCEPARAMS']._serialized_end=557
  _globals['_RUNONCERESULT']._serialized_start=559
  _globals['_RUNONCERESULT']._serialized_end=621
  _globals['_ALGORITHM']._serialized_start=624
  _globals['_ALGORITHM']._serialized_end=953
# @@protoc_insertion_point(module_scope)
