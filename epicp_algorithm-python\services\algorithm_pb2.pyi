"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""

from builtins import (
    bool,
    int,
    str,
)
from collections.abc import (
    Iterable,
)
from google.protobuf.descriptor import (
    Descriptor,
    FileDescriptor,
)
from google.protobuf.internal.containers import (
    RepeatedCompositeFieldContainer,
)
from google.protobuf.message import (
    Message,
)
from topologic_pb2 import (
    Branch,
    Edge,
    EquipData,
    Equipment,
    Node,
    Topo,
)
from typing import (
    Literal,
    final,
)

DESCRIPTOR: FileDescriptor

@final
class InitParams(Message):
    DESCRIPTOR: Descriptor

    ID_FIELD_NUMBER: int
    TOPO_FIELD_NUMBER: int
    OPTION_FIELD_NUMBER: int
    id: str
    """算法ID"""
    option: str
    """算法的配置数据"""
    @property
    def topo(self) -> Topo:
        """算法应用的拓扑网络"""

    def __init__(
        self,
        *,
        id: str = ...,
        topo: Topo | None = ...,
        option: str = ...,
    ) -> None: ...
    def HasField(self, field_name: Literal["topo", b"topo"]) -> bool: ...
    def ClearField(self, field_name: Literal["id", b"id", "option", b"option", "topo", b"topo"]) -> None: ...

@final
class InitResult(Message):
    DESCRIPTOR: Descriptor

    SUCCESS_FIELD_NUMBER: int
    MSG_FIELD_NUMBER: int
    success: bool
    """是否成功"""
    msg: str
    """提示信息"""
    def __init__(
        self,
        *,
        success: bool = ...,
        msg: str = ...,
    ) -> None: ...
    def ClearField(self, field_name: Literal["msg", b"msg", "success", b"success"]) -> None: ...

@final
class RunStepParams(Message):
    DESCRIPTOR: Descriptor

    ID_FIELD_NUMBER: int
    DATAS_FIELD_NUMBER: int
    id: str
    """算法ID"""
    @property
    def datas(self) -> RepeatedCompositeFieldContainer[EquipData]:
        """动态数据"""

    def __init__(
        self,
        *,
        id: str = ...,
        datas: Iterable[EquipData] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: Literal["datas", b"datas", "id", b"id"]) -> None: ...

@final
class RunStepResult(Message):
    DESCRIPTOR: Descriptor

    SUCCESS_FIELD_NUMBER: int
    RESULT_FIELD_NUMBER: int
    MSG_FIELD_NUMBER: int
    success: bool
    """是否计算成功"""
    result: str
    """计算结果"""
    msg: str
    """提示信息"""
    def __init__(
        self,
        *,
        success: bool = ...,
        result: str = ...,
        msg: str = ...,
    ) -> None: ...
    def ClearField(self, field_name: Literal["msg", b"msg", "result", b"result", "success", b"success"]) -> None: ...

@final
class RunStepAfterParams(Message):
    DESCRIPTOR: Descriptor

    ID_FIELD_NUMBER: int
    id: str
    """算法ID"""
    def __init__(
        self,
        *,
        id: str = ...,
    ) -> None: ...
    def ClearField(self, field_name: Literal["id", b"id"]) -> None: ...

@final
class RunStepBeforeParams(Message):
    DESCRIPTOR: Descriptor

    ID_FIELD_NUMBER: int
    id: str
    """算法ID"""
    def __init__(
        self,
        *,
        id: str = ...,
    ) -> None: ...
    def ClearField(self, field_name: Literal["id", b"id"]) -> None: ...

@final
class Empty(Message):
    """/ 空数据"""

    DESCRIPTOR: Descriptor

    def __init__(
        self,
    ) -> None: ...

@final
class RunOnceParams(Message):
    DESCRIPTOR: Descriptor

    EQUIPS_FIELD_NUMBER: int
    EDGES_FIELD_NUMBER: int
    NODES_FIELD_NUMBER: int
    BRANCHS_FIELD_NUMBER: int
    DATAS_FIELD_NUMBER: int
    OPTIONS_FIELD_NUMBER: int
    datas: str
    options: str
    @property
    def equips(self) -> RepeatedCompositeFieldContainer[Equipment]: ...
    @property
    def edges(self) -> RepeatedCompositeFieldContainer[Edge]: ...
    @property
    def nodes(self) -> RepeatedCompositeFieldContainer[Node]: ...
    @property
    def branchs(self) -> RepeatedCompositeFieldContainer[Branch]: ...
    def __init__(
        self,
        *,
        equips: Iterable[Equipment] | None = ...,
        edges: Iterable[Edge] | None = ...,
        nodes: Iterable[Node] | None = ...,
        branchs: Iterable[Branch] | None = ...,
        datas: str = ...,
        options: str = ...,
    ) -> None: ...
    def ClearField(self, field_name: Literal["branchs", b"branchs", "datas", b"datas", "edges", b"edges", "equips", b"equips", "nodes", b"nodes", "options", b"options"]) -> None: ...

@final
class RunOnceResult(Message):
    DESCRIPTOR: Descriptor

    IS_SUCCESS_FIELD_NUMBER: int
    DATA_FIELD_NUMBER: int
    MSG_FIELD_NUMBER: int
    is_success: bool
    data: str
    msg: str
    def __init__(
        self,
        *,
        is_success: bool = ...,
        data: str = ...,
        msg: str = ...,
    ) -> None: ...
    def ClearField(self, field_name: Literal["data", b"data", "is_success", b"is_success", "msg", b"msg"]) -> None: ...
