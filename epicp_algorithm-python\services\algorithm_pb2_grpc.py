# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from . import algorithm_pb2 as algorithm__pb2

GRPC_GENERATED_VERSION = '1.71.0'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in algorithm_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class AlgorithmStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.init = channel.unary_unary(
                '/algorithm.Algorithm/init',
                request_serializer=algorithm__pb2.InitParams.SerializeToString,
                response_deserializer=algorithm__pb2.InitResult.FromString,
                _registered_method=True)
        self.run_step_before = channel.unary_unary(
                '/algorithm.Algorithm/run_step_before',
                request_serializer=algorithm__pb2.RunStepBeforeParams.SerializeToString,
                response_deserializer=algorithm__pb2.Empty.FromString,
                _registered_method=True)
        self.run_step = channel.unary_unary(
                '/algorithm.Algorithm/run_step',
                request_serializer=algorithm__pb2.RunStepParams.SerializeToString,
                response_deserializer=algorithm__pb2.RunStepResult.FromString,
                _registered_method=True)
        self.run_step_after = channel.unary_unary(
                '/algorithm.Algorithm/run_step_after',
                request_serializer=algorithm__pb2.RunStepAfterParams.SerializeToString,
                response_deserializer=algorithm__pb2.Empty.FromString,
                _registered_method=True)
        self.run_once = channel.unary_unary(
                '/algorithm.Algorithm/run_once',
                request_serializer=algorithm__pb2.RunOnceParams.SerializeToString,
                response_deserializer=algorithm__pb2.RunOnceResult.FromString,
                _registered_method=True)


class AlgorithmServicer(object):
    """Missing associated documentation comment in .proto file."""

    def init(self, request, context):
        """/ 算法初始化
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def run_step_before(self, request, context):
        """/ 算法单步运行前的操作
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def run_step(self, request, context):
        """/ 算法单步运行
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def run_step_after(self, request, context):
        """/ 算法单步运行后的操作
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def run_once(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_AlgorithmServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'init': grpc.unary_unary_rpc_method_handler(
                    servicer.init,
                    request_deserializer=algorithm__pb2.InitParams.FromString,
                    response_serializer=algorithm__pb2.InitResult.SerializeToString,
            ),
            'run_step_before': grpc.unary_unary_rpc_method_handler(
                    servicer.run_step_before,
                    request_deserializer=algorithm__pb2.RunStepBeforeParams.FromString,
                    response_serializer=algorithm__pb2.Empty.SerializeToString,
            ),
            'run_step': grpc.unary_unary_rpc_method_handler(
                    servicer.run_step,
                    request_deserializer=algorithm__pb2.RunStepParams.FromString,
                    response_serializer=algorithm__pb2.RunStepResult.SerializeToString,
            ),
            'run_step_after': grpc.unary_unary_rpc_method_handler(
                    servicer.run_step_after,
                    request_deserializer=algorithm__pb2.RunStepAfterParams.FromString,
                    response_serializer=algorithm__pb2.Empty.SerializeToString,
            ),
            'run_once': grpc.unary_unary_rpc_method_handler(
                    servicer.run_once,
                    request_deserializer=algorithm__pb2.RunOnceParams.FromString,
                    response_serializer=algorithm__pb2.RunOnceResult.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'algorithm.Algorithm', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('algorithm.Algorithm', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class Algorithm(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def init(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/algorithm.Algorithm/init',
            algorithm__pb2.InitParams.SerializeToString,
            algorithm__pb2.InitResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def run_step_before(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/algorithm.Algorithm/run_step_before',
            algorithm__pb2.RunStepBeforeParams.SerializeToString,
            algorithm__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def run_step(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/algorithm.Algorithm/run_step',
            algorithm__pb2.RunStepParams.SerializeToString,
            algorithm__pb2.RunStepResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def run_step_after(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/algorithm.Algorithm/run_step_after',
            algorithm__pb2.RunStepAfterParams.SerializeToString,
            algorithm__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def run_once(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/algorithm.Algorithm/run_once',
            algorithm__pb2.RunOnceParams.SerializeToString,
            algorithm__pb2.RunOnceResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
