syntax = "proto3";

package algorithm;
import "topologic.proto";

message InitParams {
   string id=1; // 算法ID
   topologic.Topo topo = 2; //算法应用的拓扑网络
   string option = 3; // 算法的配置数据
}
message InitResult {
   bool success = 1; // 是否成功
   string msg = 2; // 提示信息
}

message RunStepParams {
    string id = 1; // 算法ID
    repeated topologic.EquipData datas = 2; // 动态数据
}

message RunStepResult {
    bool success = 1; // 是否计算成功
    string result = 2; // 计算结果
    string msg = 3; // 提示信息
}

message RunStepAfterParams{
    string id = 1; // 算法ID
}
message RunStepBeforeParams{
    string id = 1; // 算法ID
}
/// 空数据
message Empty {}

message RunOnceParams {
   repeated topologic.Equipment equips = 1;
   repeated topologic.Edge edges = 2;
   repeated topologic.Node nodes = 3;
   repeated topologic.Branch branchs = 4;
   string datas = 5;
   string options = 6;
}
message RunOnceResult {
   bool is_success = 1;
   string data=2;
   string msg = 3;
}

service Algorithm {
    /// 算法初始化
    rpc init (InitParams) returns (InitResult);
    /// 算法单步运行前的操作
    rpc run_step_before(RunStepBeforeParams) returns (Empty);
    /// 算法单步运行
    rpc run_step(RunStepParams) returns (RunStepResult);
    /// 算法单步运行后的操作
    rpc run_step_after(RunStepAfterParams) returns (Empty);

    rpc run_once(RunOnceParams) returns (RunOnceResult);
}  