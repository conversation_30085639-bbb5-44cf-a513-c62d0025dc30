syntax = "proto3";

package topologic;

// 节点设备类型
message NodeTypes {
  repeated string types = 1; //类型列表
}
// 设备
message Equipment{
  string id = 1; // 设备ID 全局唯一
  string name = 2; //设备名称
  string type = 3; //设备类型
  string data = 4; //设备的其他静态数据 json_string
}
// 设备间的连接关系
message Edge {
  string id =1; //边的ID
  string source_id = 2; // 边的源设备ID
  string target_id = 3; // 边的终点设备ID
  string source_port = 4; // source_id设备的连接端口
  string target_port = 5; // target_id设备的连接端口
}

// 支路
message Branch {
  string start_node_id=1; //开始设备
  string start_node_name=2;//开始设备
  string end_node_id=3;//结束设备
  string end_node_name=4;//结束设备
  repeated string other_equips=5;//从开始设备到结束设备的列表
}
// 节点的附属设备
message NodeSubEquip{
  repeated string ids = 1; //节点附属设备列表
}
// 节点
message Node {
 string id=1;// 节点设备ID
 repeated NodeSubEquip other_equips = 2;//从其他设备连接到本节点设备的设备列表
}

// 拓扑数据
message Topo {
  string id =1; //拓扑图的ID
  repeated Equipment equips=2; //设备列表
  repeated Edge edges =3;//连接关系列表
  repeated Node nodes = 4;//节点设备列表
  repeated Branch branchs = 5;//支路列表
}

message EquipData {
  string id = 1; // 设备ID
  /**
  设备数据JSON格式
  比如：
     {
      "u": 12.0,
      "p": 13.0,
      "state": 1,
    }
  */
  string data = 2; // 设备数据 JSON
  int64  state = 3; // 设备状态 默认为1
}