# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: topologic.proto
# Protobuf Python Version: 5.29.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    0,
    '',
    'topologic.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0ftopologic.proto\x12\ttopologic\"\x1a\n\tNodeTypes\x12\r\n\x05types\x18\x01 \x03(\t\"A\n\tEquipment\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x0c\n\x04type\x18\x03 \x01(\t\x12\x0c\n\x04\x64\x61ta\x18\x04 \x01(\t\"b\n\x04\x45\x64ge\x12\n\n\x02id\x18\x01 \x01(\t\x12\x11\n\tsource_id\x18\x02 \x01(\t\x12\x11\n\ttarget_id\x18\x03 \x01(\t\x12\x13\n\x0bsource_port\x18\x04 \x01(\t\x12\x13\n\x0btarget_port\x18\x05 \x01(\t\"z\n\x06\x42ranch\x12\x15\n\rstart_node_id\x18\x01 \x01(\t\x12\x17\n\x0fstart_node_name\x18\x02 \x01(\t\x12\x13\n\x0b\x65nd_node_id\x18\x03 \x01(\t\x12\x15\n\rend_node_name\x18\x04 \x01(\t\x12\x14\n\x0cother_equips\x18\x05 \x03(\t\"\x1b\n\x0cNodeSubEquip\x12\x0b\n\x03ids\x18\x01 \x03(\t\"A\n\x04Node\x12\n\n\x02id\x18\x01 \x01(\t\x12-\n\x0cother_equips\x18\x02 \x03(\x0b\x32\x17.topologic.NodeSubEquip\"\x9c\x01\n\x04Topo\x12\n\n\x02id\x18\x01 \x01(\t\x12$\n\x06\x65quips\x18\x02 \x03(\x0b\x32\x14.topologic.Equipment\x12\x1e\n\x05\x65\x64ges\x18\x03 \x03(\x0b\x32\x0f.topologic.Edge\x12\x1e\n\x05nodes\x18\x04 \x03(\x0b\x32\x0f.topologic.Node\x12\"\n\x07\x62ranchs\x18\x05 \x03(\x0b\x32\x11.topologic.Branch\"4\n\tEquipData\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04\x64\x61ta\x18\x02 \x01(\t\x12\r\n\x05state\x18\x03 \x01(\x03\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'topologic_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_NODETYPES']._serialized_start=30
  _globals['_NODETYPES']._serialized_end=56
  _globals['_EQUIPMENT']._serialized_start=58
  _globals['_EQUIPMENT']._serialized_end=123
  _globals['_EDGE']._serialized_start=125
  _globals['_EDGE']._serialized_end=223
  _globals['_BRANCH']._serialized_start=225
  _globals['_BRANCH']._serialized_end=347
  _globals['_NODESUBEQUIP']._serialized_start=349
  _globals['_NODESUBEQUIP']._serialized_end=376
  _globals['_NODE']._serialized_start=378
  _globals['_NODE']._serialized_end=443
  _globals['_TOPO']._serialized_start=446
  _globals['_TOPO']._serialized_end=602
  _globals['_EQUIPDATA']._serialized_start=604
  _globals['_EQUIPDATA']._serialized_end=656
# @@protoc_insertion_point(module_scope)
