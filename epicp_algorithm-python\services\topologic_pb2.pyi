"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""

from builtins import (
    int,
    str,
)
from collections.abc import (
    Iterable,
)
from google.protobuf.descriptor import (
    Descriptor,
    FileDescriptor,
)
from google.protobuf.internal.containers import (
    RepeatedCompositeFieldContainer,
    RepeatedScalarFieldContainer,
)
from google.protobuf.message import (
    Message,
)
from typing import (
    Literal,
    final,
)

DESCRIPTOR: FileDescriptor

@final
class NodeTypes(Message):
    """节点设备类型"""

    DESCRIPTOR: Descriptor

    TYPES_FIELD_NUMBER: int
    @property
    def types(self) -> RepeatedScalarFieldContainer[str]:
        """类型列表"""

    def __init__(
        self,
        *,
        types: Iterable[str] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: Literal["types", b"types"]) -> None: ...

@final
class Equipment(Message):
    """设备"""

    DESCRIPTOR: Descriptor

    ID_FIELD_NUMBER: int
    NAME_FIELD_NUMBER: int
    TYPE_FIELD_NUMBER: int
    DATA_FIELD_NUMBER: int
    id: str
    """设备ID 全局唯一"""
    name: str
    """设备名称"""
    type: str
    """设备类型"""
    data: str
    """设备的其他静态数据 json_string"""
    def __init__(
        self,
        *,
        id: str = ...,
        name: str = ...,
        type: str = ...,
        data: str = ...,
    ) -> None: ...
    def ClearField(self, field_name: Literal["data", b"data", "id", b"id", "name", b"name", "type", b"type"]) -> None: ...

@final
class Edge(Message):
    """设备间的连接关系"""

    DESCRIPTOR: Descriptor

    ID_FIELD_NUMBER: int
    SOURCE_ID_FIELD_NUMBER: int
    TARGET_ID_FIELD_NUMBER: int
    SOURCE_PORT_FIELD_NUMBER: int
    TARGET_PORT_FIELD_NUMBER: int
    id: str
    """边的ID"""
    source_id: str
    """边的源设备ID"""
    target_id: str
    """边的终点设备ID"""
    source_port: str
    """source_id设备的连接端口"""
    target_port: str
    """target_id设备的连接端口"""
    def __init__(
        self,
        *,
        id: str = ...,
        source_id: str = ...,
        target_id: str = ...,
        source_port: str = ...,
        target_port: str = ...,
    ) -> None: ...
    def ClearField(self, field_name: Literal["id", b"id", "source_id", b"source_id", "source_port", b"source_port", "target_id", b"target_id", "target_port", b"target_port"]) -> None: ...

@final
class Branch(Message):
    """支路"""

    DESCRIPTOR: Descriptor

    START_NODE_ID_FIELD_NUMBER: int
    START_NODE_NAME_FIELD_NUMBER: int
    END_NODE_ID_FIELD_NUMBER: int
    END_NODE_NAME_FIELD_NUMBER: int
    OTHER_EQUIPS_FIELD_NUMBER: int
    start_node_id: str
    """开始设备"""
    start_node_name: str
    """开始设备"""
    end_node_id: str
    """结束设备"""
    end_node_name: str
    """结束设备"""
    @property
    def other_equips(self) -> RepeatedScalarFieldContainer[str]:
        """从开始设备到结束设备的列表"""

    def __init__(
        self,
        *,
        start_node_id: str = ...,
        start_node_name: str = ...,
        end_node_id: str = ...,
        end_node_name: str = ...,
        other_equips: Iterable[str] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: Literal["end_node_id", b"end_node_id", "end_node_name", b"end_node_name", "other_equips", b"other_equips", "start_node_id", b"start_node_id", "start_node_name", b"start_node_name"]) -> None: ...

@final
class NodeSubEquip(Message):
    """节点的附属设备"""

    DESCRIPTOR: Descriptor

    IDS_FIELD_NUMBER: int
    @property
    def ids(self) -> RepeatedScalarFieldContainer[str]:
        """节点附属设备列表"""

    def __init__(
        self,
        *,
        ids: Iterable[str] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: Literal["ids", b"ids"]) -> None: ...

@final
class Node(Message):
    """节点"""

    DESCRIPTOR: Descriptor

    ID_FIELD_NUMBER: int
    OTHER_EQUIPS_FIELD_NUMBER: int
    id: str
    """节点设备ID"""
    @property
    def other_equips(self) -> RepeatedCompositeFieldContainer[NodeSubEquip]:
        """从其他设备连接到本节点设备的设备列表"""

    def __init__(
        self,
        *,
        id: str = ...,
        other_equips: Iterable[NodeSubEquip] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: Literal["id", b"id", "other_equips", b"other_equips"]) -> None: ...

@final
class Topo(Message):
    """拓扑数据"""

    DESCRIPTOR: Descriptor

    ID_FIELD_NUMBER: int
    EQUIPS_FIELD_NUMBER: int
    EDGES_FIELD_NUMBER: int
    NODES_FIELD_NUMBER: int
    BRANCHS_FIELD_NUMBER: int
    id: str
    """拓扑图的ID"""
    @property
    def equips(self) -> RepeatedCompositeFieldContainer[Equipment]:
        """设备列表"""

    @property
    def edges(self) -> RepeatedCompositeFieldContainer[Edge]:
        """连接关系列表"""

    @property
    def nodes(self) -> RepeatedCompositeFieldContainer[Node]:
        """节点设备列表"""

    @property
    def branchs(self) -> RepeatedCompositeFieldContainer[Branch]:
        """支路列表"""

    def __init__(
        self,
        *,
        id: str = ...,
        equips: Iterable[Equipment] | None = ...,
        edges: Iterable[Edge] | None = ...,
        nodes: Iterable[Node] | None = ...,
        branchs: Iterable[Branch] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: Literal["branchs", b"branchs", "edges", b"edges", "equips", b"equips", "id", b"id", "nodes", b"nodes"]) -> None: ...

@final
class EquipData(Message):
    DESCRIPTOR: Descriptor

    ID_FIELD_NUMBER: int
    DATA_FIELD_NUMBER: int
    STATE_FIELD_NUMBER: int
    id: str
    """设备ID"""
    data: str
    """*
    设备数据JSON格式
    比如：
    {
    "u": 12.0,
    "p": 13.0,
    "state": 1,
    }
    设备数据 JSON
    """
    state: int
    """设备状态 默认为1"""
    def __init__(
        self,
        *,
        id: str = ...,
        data: str = ...,
        state: int = ...,
    ) -> None: ...
    def ClearField(self, field_name: Literal["data", b"data", "id", b"id", "state", b"state"]) -> None: ...
