from services.algorithm_pb2_grpc import AlgorithmServicer
from services.algorithm_pb2 import RunOnceParams,RunOnceResult
import json

class MyAlgorithm(AlgorithmServicer):
   
   def run_once(self,request: RunOnceParams,context)->RunOnceResult:
      """
      对于电压预测算法来说
      只需要用到request.options
      """
      #region 拓扑网络数据
      print(request.equips) #网络中的设备列表
      print(request.edges) #网络中设备的连线列表
      print(request.nodes)#网络中的“节点设备”列表 包含节点的并联支路关系
      print(request.branchs)#网络中的“支路”列表 包含支路中串联的设备
      print(request.datas)#网络中的设备的动态数据列表 可以理解为一个运行断面 包含诸如母线电压、负荷功率... json字符串格式
      #endregion
      print(request.options)#算法的配置数据 json字符串格式 先用json解析
      # json_data = json.loads(str_data)  # 使用json.loads()函数将字符串转换为JSON对象
      # print(json_data)
      return RunOnceResult(is_success=True,data = '',msg='')