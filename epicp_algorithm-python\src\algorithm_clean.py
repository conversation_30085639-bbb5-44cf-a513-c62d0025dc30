from services.algorithm_pb2_grpc import AlgorithmServicer
from services.algorithm_pb2 import RunOnceParams,RunOnceResult
import json

#region 电压预测功能模块 - 新增部分
import os
import sys

# 添加时间序列预测模块路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'MyAlgorithm', 'Time-Series-AVC'))

try:
    from run_for_api import run_prediction_api
    TIMESERIES_AVAILABLE = True
    print("时间序列预测模块加载成功")
except ImportError as e:
    print(f"时间序列模块导入失败: {e}")
    TIMESERIES_AVAILABLE = False
#endregion

class MyAlgorithm(AlgorithmServicer):
   
    def run_once(self,request: RunOnceParams,context)->RunOnceResult:
        """
        对于电压预测算法来说
        只需要用到request.options
        """
        #region 拓扑网络数据
        print(request.equips) #网络中的设备列表
        print(request.edges) #网络中设备的连线列表
        print(request.nodes)#网络中的"节点设备"列表 包含节点的并联支路关系
        print(request.branchs)#网络中的"支路"列表 包含支路中串联的设备
        print(request.datas)#网络中的设备的动态数据列表 可以理解为一个运行断面 包含诸如母线电压、负荷功率... json字符串格式
        #endregion
        print(request.options)#算法的配置数据 json字符串格式 先用json解析
        # json_data = json.loads(str_data)  # 使用json.loads()函数将字符串转换为JSON对象
        # print(json_data)
        
        #region 电压预测功能实现 - 新增部分
        try:
            time_series_data = []
            pred_len = 7  # 默认预测7天
            seq_len = 28  # 默认序列长度28天
            
            if request.options:
                try:
                    options_data = json.loads(request.options)
                    # 检查options中是否包含时间序列数据
                    if isinstance(options_data, list) and len(options_data) > 0:
                        # 客户端把时间序列数据放在了options字段中
                        print("检测到客户端将时间序列数据放在options字段中，进行适配")
                        time_series_data = options_data
                    elif isinstance(options_data, dict):
                        # 正常的配置数据
                        pred_len = options_data.get('prediction_days', 7)
                        seq_len = options_data.get('sequence_length', 28)
                except json.JSONDecodeError as e:
                    print(f"配置解析失败: {e}")
            
            # 获取时间序列数据
            input_data = []
            if time_series_data:
                input_data = time_series_data
                print(f"从options字段获取时间序列数据: {len(input_data)} 条记录")
            elif request.datas:
                try:
                    input_data = json.loads(request.datas)
                    print(f"从datas字段获取时间序列数据: {len(input_data)} 条记录")
                except json.JSONDecodeError as e:
                    print(f"输入数据解析失败: {e}")
                    return RunOnceResult(is_success=False, data='', msg=f'输入数据格式错误: {str(e)}')
            
            # 检查数据是否为空
            if not input_data:
                return RunOnceResult(is_success=False, data='', msg='未提供时间序列数据')
            
            # 执行电压预测
            if TIMESERIES_AVAILABLE:
                print("使用时间序列预测模型")
                prediction_result = run_prediction_api(input_data, pred_len=pred_len, seq_len=seq_len)
            else:
                print("时间序列模块不可用，使用简单预测方法")
                prediction_result = self._simple_prediction_fallback(input_data, pred_len)
            
            # 检查预测结果
            if "error" in prediction_result:
                return RunOnceResult(is_success=False, data='', msg=f'预测失败: {prediction_result["error"]}')
            
            # 将预测结果转换为JSON字符串
            result_json = json.dumps(prediction_result, ensure_ascii=False, indent=2)
            
            print("=== 电压预测算法执行完成 ===")
            print(f"预测结果摘要: 成功预测 {len(prediction_result.get('predictions', []))} 个时间点")
            
            return RunOnceResult(is_success=True, data=result_json, msg='电压预测完成')
            
        except Exception as e:
            error_msg = f"算法执行异常: {str(e)}"
            print(error_msg)
            return RunOnceResult(is_success=False, data='', msg=error_msg)
        #endregion
    
    #region 简单预测回退方法 - 新增部分
    def _simple_prediction_fallback(self, input_data, pred_len):
        """简单预测回退方法（不依赖numpy）"""
        try:
            import random
            from datetime import datetime, timedelta
            
            # 提取电压值和时间
            values = []
            dates = []
            
            for item in input_data:
                # 提取数值
                value = None
                if 'value' in item:
                    value = float(item['value'])
                elif 'vol' in item:
                    value = float(item['vol'])
                elif 'voltage' in item:
                    value = float(item['voltage'])
                elif '电压' in item:
                    value = float(item['电压'])
                
                # 提取时间
                time_str = None
                if 'time' in item:
                    time_str = item['time']
                elif 'date' in item:
                    time_str = item['date']
                elif 'timestamp' in item:
                    time_str = item['timestamp']
                elif '时间' in item:
                    time_str = item['时间']
                
                if value is not None and time_str:
                    values.append(value)
                    dates.append(time_str)
            
            if len(values) == 0:
                return {"error": "未找到有效的数值数据"}
            
            # 使用移动平均和趋势分析（纯Python实现）
            window_size = min(7, len(values))
            recent_values = values[-window_size:]
            recent_avg = sum(recent_values) / len(recent_values)
            
            # 计算趋势
            if len(values) >= 2:
                trend_window = min(7, len(values))
                trend = (values[-1] - values[-trend_window]) / trend_window
            else:
                trend = 0
            
            # 生成预测值
            predictions = []
            for i in range(pred_len):
                # 基于趋势和随机波动生成预测值
                pred_value = recent_avg + trend * (i + 1) + random.uniform(-0.02, 0.02)
                predictions.append(pred_value)
            
            # 生成预测日期
            try:
                # 尝试解析最后一个日期
                last_date_str = dates[-1].split()[0]  # 去掉时间部分
                try:
                    last_date = datetime.strptime(last_date_str, '%Y-%m-%d')
                except ValueError:
                    try:
                        last_date = datetime.strptime(last_date_str, '%Y/%m/%d')
                    except ValueError:
                        last_date = datetime.now()
            except:
                last_date = datetime.now()
            
            pred_dates = [last_date + timedelta(days=i+1) for i in range(pred_len)]
            
            result = {
                "success": True,
                "predictions": [
                    {
                        "date": date.strftime('%Y-%m-%d'),
                        "predicted_voltage": float(voltage)
                    }
                    for date, voltage in zip(pred_dates, predictions)
                ],
                "model_info": {
                    "model_type": "Simple Moving Average (Fallback)",
                    "sequence_length": len(values),
                    "prediction_length": pred_len
                },
                "input_summary": {
                    "data_points": len(values),
                    "voltage_range": [min(values), max(values)],
                    "last_voltage": values[-1] if values else None
                },
                "note": "使用简单预测方法，建议使用完整的时间序列模型以获得更好效果"
            }
            
            return result
            
        except Exception as e:
            import traceback
            traceback.print_exc()
            return {"error": f"简单预测失败: {str(e)}"}
    #endregion
