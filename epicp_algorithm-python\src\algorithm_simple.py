from services.algorithm_pb2_grpc import AlgorithmServicer
from services.algorithm_pb2 import RunOnceParams,RunOnceResult
import json

#region 电压预测功能模块 - 新增部分
import os
import sys

# 添加时间序列预测模块路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'MyAlgorithm', 'Time-Series-AVC'))

try:
    from run_for_api import run_prediction_api
    TIMESERIES_AVAILABLE = True
    print("时间序列预测模块加载成功")
except ImportError as e:
    print(f"时间序列模块导入失败: {e}")
    TIMESERIES_AVAILABLE = False
#endregion

class MyAlgorithm(AlgorithmServicer):
   
    def run_once(self,request: RunOnceParams,context)->RunOnceResult:
        """
        对于电压预测算法来说
        只需要用到request.options
        """
        #region 拓扑网络数据
        print(request.equips) #网络中的设备列表
        print(request.edges) #网络中设备的连线列表
        print(request.nodes)#网络中的"节点设备"列表 包含节点的并联支路关系
        print(request.branchs)#网络中的"支路"列表 包含支路中串联的设备
        print(request.datas)#网络中的设备的动态数据列表 可以理解为一个运行断面 包含诸如母线电压、负荷功率... json字符串格式
        #endregion
        print(request.options)#算法的配置数据 json字符串格式 先用json解析
        # json_data = json.loads(str_data)  # 使用json.loads()函数将字符串转换为JSON对象
        # print(json_data)
        
        #region 电压预测功能实现 - 新增部分
        try:
            # 直接从options字段获取时间序列数据
            pred_len = 7  # 默认预测7天
            seq_len = 28  # 默认序列长度28天
            
            if not request.options:
                return RunOnceResult(is_success=False, data='', msg='未提供时间序列数据')
            
            try:
                input_data = json.loads(request.options)
                print(f"从options字段获取时间序列数据: {len(input_data)} 条记录")
            except json.JSONDecodeError as e:
                print(f"输入数据解析失败: {e}")
                return RunOnceResult(is_success=False, data='', msg=f'输入数据格式错误: {str(e)}')
            
            # 检查数据是否为空
            if not input_data:
                return RunOnceResult(is_success=False, data='', msg='未提供时间序列数据')
            
            # 执行电压预测 - 只使用时间序列模型
            if not TIMESERIES_AVAILABLE:
                return RunOnceResult(is_success=False, data='', msg='时间序列预测模块不可用')
            
            print("使用时间序列预测模型")
            prediction_result = run_prediction_api(input_data, pred_len=pred_len, seq_len=seq_len)
            
            # 检查预测结果
            if "error" in prediction_result:
                return RunOnceResult(is_success=False, data='', msg=f'预测失败: {prediction_result["error"]}')
            
            # 将预测结果转换为JSON字符串
            result_json = json.dumps(prediction_result, ensure_ascii=False, indent=2)
            
            print("=== 电压预测算法执行完成 ===")
            print(f"预测结果摘要: 成功预测 {len(prediction_result.get('predictions', []))} 个时间点")
            
            return RunOnceResult(is_success=True, data=result_json, msg='电压预测完成')
            
        except Exception as e:
            error_msg = f"算法执行异常: {str(e)}"
            print(error_msg)
            return RunOnceResult(is_success=False, data='', msg=error_msg)
        #endregion
