#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
gRPC客户端测试脚本
用于测试电压预测服务
"""

import grpc
import json
import sys
from datetime import datetime, timedelta

# 导入gRPC生成的类
from services.algorithm_pb2_grpc import AlgorithmStub
from services.algorithm_pb2 import RunOnceParams
from services.topologic_pb2 import Equipment, Edge, Node, Branch, NodeSubEquip

def test_voltage_prediction_service():
    """测试电压预测服务"""
    
    # 连接到服务器
    try:
        channel = grpc.insecure_channel('localhost:50051')
        stub = AlgorithmStub(channel)
        print("✓ 成功连接到gRPC服务器 (localhost:50051)")
    except Exception as e:
        print(f"✗ 连接服务器失败: {e}")
        return False
    
    # 准备测试数据
    print("\n准备测试数据...")
    
    # 生成30天的模拟电压数据
    time_series_data = []
    base_date = datetime(2024, 1, 1)
    base_voltage = 10.3
    
    for i in range(30):
        date_str = (base_date + timedelta(days=i)).strftime('%Y-%m-%d')
        # 模拟电压变化：基础值 + 周期性变化 + 随机波动
        voltage = base_voltage + 0.1 * (i % 7) + 0.05 * (i % 3) + (i * 0.001)
        time_series_data.append({
            "time": date_str,
            "value": round(voltage, 3)
        })
    
    print(f"✓ 生成了 {len(time_series_data)} 天的历史电压数据")
    print(f"  数据范围: {time_series_data[0]['time']} 到 {time_series_data[-1]['time']}")
    print(f"  电压范围: {min(d['value'] for d in time_series_data):.3f}V 到 {max(d['value'] for d in time_series_data):.3f}V")
    
    # 配置预测参数
    options = {
        "prediction_days": 7,
        "sequence_length": 28,
        "model_type": "informer"
    }
    
    print(f"✓ 预测配置: 预测未来 {options['prediction_days']} 天")
    
    # 创建模拟的设备对象
    equips = [
        Equipment(id="BUS001", name="母线1", type="BUS", data="{}"),
        Equipment(id="TRANS001", name="变压器1", type="TRANSFORMER", data="{}"),
        Equipment(id="LOAD001", name="负荷1", type="LOAD", data="{}")
    ]

    # 创建模拟的连线对象
    edges = [
        Edge(id="EDGE001", source_id="BUS001", target_id="TRANS001", source_port="1", target_port="1"),
        Edge(id="EDGE002", source_id="TRANS001", target_id="LOAD001", source_port="2", target_port="1")
    ]

    # 创建模拟的节点对象
    nodes = [
        Node(id="BUS001", other_equips=[NodeSubEquip(ids=["TRANS001"])]),
        Node(id="TRANS001", other_equips=[NodeSubEquip(ids=["LOAD001"])])
    ]

    # 创建模拟的支路对象
    branchs = [
        Branch(start_node_id="BUS001", start_node_name="母线1",
               end_node_id="LOAD001", end_node_name="负荷1",
               other_equips=["TRANS001"])
    ]

    # 创建gRPC请求
    request = RunOnceParams(
        equips=equips,                        # 设备列表
        edges=edges,                          # 连线列表
        nodes=nodes,                          # 节点列表
        branchs=branchs,                      # 支路列表
        datas=json.dumps(time_series_data),   # 时间序列数据
        options=json.dumps(options)           # 算法配置
    )
    
    print("\n发送预测请求...")
    
    # 调用服务
    try:
        response = stub.run_once(request)
        print("✓ 成功接收到服务响应")
    except Exception as e:
        print(f"✗ 服务调用失败: {e}")
        return False
    
    # 处理响应
    print(f"\n服务执行结果:")
    print(f"  成功状态: {response.is_success}")
    print(f"  消息: {response.msg}")
    
    if response.is_success and response.data:
        try:
            result = json.loads(response.data)
            print(f"\n预测结果:")
            print(f"  预测成功: {result.get('success', False)}")
            
            predictions = result.get('predictions', [])
            if predictions:
                print(f"  预测天数: {len(predictions)}")
                print(f"  预测日期范围: {predictions[0]['date']} 到 {predictions[-1]['date']}")
                
                print(f"\n详细预测结果:")
                for i, pred in enumerate(predictions):
                    print(f"    {pred['date']}: {pred['predicted_voltage']:.3f}V")
                
                # 显示模型信息
                model_info = result.get('model_info', {})
                print(f"\n模型信息:")
                print(f"  模型类型: {model_info.get('model_type', 'Unknown')}")
                print(f"  输入序列长度: {model_info.get('sequence_length', 'Unknown')}")
                print(f"  预测长度: {model_info.get('prediction_length', 'Unknown')}")
                
                # 显示输入数据摘要
                input_summary = result.get('input_summary', {})
                if input_summary:
                    print(f"\n输入数据摘要:")
                    print(f"  数据点数: {input_summary.get('data_points', 'Unknown')}")
                    voltage_range = input_summary.get('voltage_range', [])
                    if voltage_range:
                        print(f"  电压范围: {voltage_range[0]:.3f}V 到 {voltage_range[1]:.3f}V")
                    last_voltage = input_summary.get('last_voltage')
                    if last_voltage:
                        print(f"  最后电压: {last_voltage:.3f}V")
                
                # 显示注意事项
                note = result.get('note')
                if note:
                    print(f"\n注意: {note}")
                
                return True
            else:
                print("  ✗ 未获得预测结果")
                return False
                
        except json.JSONDecodeError as e:
            print(f"  ✗ 响应数据解析失败: {e}")
            print(f"  原始数据: {response.data[:200]}...")
            return False
    else:
        print(f"  ✗ 预测失败")
        return False

def test_error_cases():
    """测试错误情况"""
    print("\n" + "="*60)
    print("测试错误处理...")
    
    try:
        channel = grpc.insecure_channel('localhost:50051')
        stub = AlgorithmStub(channel)
    except Exception as e:
        print(f"✗ 连接服务器失败: {e}")
        return
    
    # 测试空数据
    print("\n1. 测试空数据...")
    request = RunOnceParams(
        equips=[],
        edges=[],
        nodes=[],
        branchs=[],
        datas="",  # 空数据
        options="{}"
    )

    response = stub.run_once(request)
    print(f"   结果: {response.is_success}, 消息: {response.msg}")

    # 测试无效JSON
    print("\n2. 测试无效JSON...")
    request = RunOnceParams(
        equips=[],
        edges=[],
        nodes=[],
        branchs=[],
        datas="invalid json",  # 无效JSON
        options="{}"
    )

    response = stub.run_once(request)
    print(f"   结果: {response.is_success}, 消息: {response.msg}")

def main():
    """主函数"""
    print("电压预测gRPC服务测试")
    print("="*60)
    
    # 测试正常预测
    success = test_voltage_prediction_service()
    
    if success:
        print("\n✓ 电压预测服务测试通过！")
        
        # 测试错误情况
        test_error_cases()
        
        print("\n" + "="*60)
        print("所有测试完成！服务运行正常。")
    else:
        print("\n✗ 电压预测服务测试失败！")
        print("\n请检查:")
        print("1. 服务是否已启动 (python main.py)")
        print("2. 端口50051是否可用")
        print("3. 依赖包是否正确安装")

if __name__ == "__main__":
    main()
