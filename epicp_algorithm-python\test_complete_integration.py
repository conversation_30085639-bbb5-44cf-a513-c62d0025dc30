#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的电压预测算法集成测试
"""

import sys
import os
import json
from datetime import datetime, timedelta

# 添加src路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from algorithm import MyAlgorithm

def test_api_prediction():
    """测试API预测功能"""
    print("=== 测试API预测功能 ===")
    
    # 导入API预测模块
    sys.path.append(os.path.join(os.path.dirname(__file__), 'MyAlgorithm', 'Time-Series-AVC'))
    
    try:
        from run_for_api import run_prediction_api
        
        # 测试数据
        test_data = [
            {"time": "2024-01-01", "value": 10.3},
            {"time": "2024-01-02", "value": 10.35},
            {"time": "2024-01-03", "value": 10.28},
            {"time": "2024-01-04", "value": 10.42},
            {"time": "2024-01-05", "value": 10.38},
            {"time": "2024-01-06", "value": 10.33},
            {"time": "2024-01-07", "value": 10.41}
        ]
        
        print(f"输入数据: {len(test_data)} 条记录")
        
        # 执行预测
        result = run_prediction_api(test_data, pred_len=7, seq_len=7)
        
        print("API预测结果:")
        print(json.dumps(result, ensure_ascii=False, indent=2))
        
        return result.get('success', False)
        
    except ImportError as e:
        print(f"API模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"API预测测试失败: {e}")
        return False

def test_grpc_service():
    """测试gRPC服务"""
    print("\n=== 测试gRPC服务 ===")
    
    # 创建模拟的gRPC请求对象
    class MockRequest:
        def __init__(self):
            self.equips = ["设备1", "设备2", "设备3"]
            self.edges = ["连线1", "连线2"]
            self.nodes = ["节点1", "节点2"]
            self.branchs = ["支路1"]
            
            # 时间序列数据 - 这是关键的输入
            time_series_data = []
            base_date = datetime(2024, 1, 1)
            for i in range(30):  # 30天的历史数据
                date_str = (base_date + timedelta(days=i)).strftime('%Y-%m-%d')
                voltage = 10.3 + 0.1 * (i % 7) + 0.05 * (i % 3)  # 模拟电压变化
                time_series_data.append({
                    "time": date_str,
                    "value": voltage
                })
            
            self.datas = json.dumps(time_series_data)
            
            # 算法配置
            self.options = json.dumps({
                "prediction_days": 7,
                "sequence_length": 28,
                "model_type": "informer"
            })
    
    # 创建算法实例
    algorithm = MyAlgorithm()
    
    # 创建模拟请求
    request = MockRequest()
    
    print(f"输入数据长度: {len(json.loads(request.datas))} 条记录")
    print(f"配置参数: {request.options}")
    
    # 执行算法
    result = algorithm.run_once(request, None)
    
    print(f"\n执行结果:")
    print(f"成功: {result.is_success}")
    print(f"消息: {result.msg}")
    
    if result.data:
        print("预测数据:")
        try:
            data = json.loads(result.data)
            print(json.dumps(data, ensure_ascii=False, indent=2))
            
            # 验证结果格式
            if data.get('success') and 'predictions' in data:
                predictions = data['predictions']
                print(f"\n预测摘要:")
                print(f"- 预测天数: {len(predictions)}")
                if predictions:
                    print(f"- 第一天预测: {predictions[0]['date']} -> {predictions[0]['predicted_voltage']:.3f}")
                    print(f"- 最后一天预测: {predictions[-1]['date']} -> {predictions[-1]['predicted_voltage']:.3f}")
                
                return True
            else:
                print("预测结果格式不正确")
                return False
                
        except json.JSONDecodeError as e:
            print(f"结果解析失败: {e}")
            print(f"原始数据: {result.data}")
            return False
    else:
        print("未返回预测数据")
        return False

def test_error_handling():
    """测试错误处理"""
    print("\n=== 测试错误处理 ===")
    
    class MockErrorRequest:
        def __init__(self, error_type):
            self.equips = []
            self.edges = []
            self.nodes = []
            self.branchs = []
            
            if error_type == "empty_data":
                self.datas = ""
                self.options = "{}"
            elif error_type == "invalid_json":
                self.datas = "invalid json"
                self.options = "{}"
            elif error_type == "no_time_data":
                self.datas = json.dumps([{"no_time": "data"}])
                self.options = "{}"
    
    algorithm = MyAlgorithm()
    
    # 测试空数据
    print("测试空数据...")
    request = MockErrorRequest("empty_data")
    result = algorithm.run_once(request, None)
    print(f"空数据测试 - 成功: {not result.is_success}, 消息: {result.msg}")
    
    # 测试无效JSON
    print("测试无效JSON...")
    request = MockErrorRequest("invalid_json")
    result = algorithm.run_once(request, None)
    print(f"无效JSON测试 - 成功: {not result.is_success}, 消息: {result.msg}")
    
    # 测试无时间数据
    print("测试无时间数据...")
    request = MockErrorRequest("no_time_data")
    result = algorithm.run_once(request, None)
    print(f"无时间数据测试 - 成功: {result.is_success}, 消息: {result.msg}")

def test_different_data_formats():
    """测试不同的数据格式"""
    print("\n=== 测试不同数据格式 ===")
    
    class MockFormatRequest:
        def __init__(self, data_format):
            self.equips = []
            self.edges = []
            self.nodes = []
            self.branchs = []
            self.options = json.dumps({"prediction_days": 3})
            
            if data_format == "standard":
                self.datas = json.dumps([
                    {"time": "2024-01-01", "value": 10.3},
                    {"time": "2024-01-02", "value": 10.35}
                ])
            elif data_format == "chinese":
                self.datas = json.dumps([
                    {"时间": "2024-01-01", "电压": 10.3},
                    {"时间": "2024-01-02", "电压": 10.35}
                ])
            elif data_format == "voltage":
                self.datas = json.dumps([
                    {"date": "2024-01-01", "voltage": 10.3},
                    {"date": "2024-01-02", "voltage": 10.35}
                ])
    
    algorithm = MyAlgorithm()
    
    formats = ["standard", "chinese", "voltage"]
    for fmt in formats:
        print(f"测试 {fmt} 格式...")
        request = MockFormatRequest(fmt)
        result = algorithm.run_once(request, None)
        print(f"{fmt} 格式测试 - 成功: {result.is_success}")
        if result.is_success and result.data:
            data = json.loads(result.data)
            pred_count = len(data.get('predictions', []))
            print(f"  预测数量: {pred_count}")

def main():
    """主测试函数"""
    print("电压预测算法完整集成测试")
    print("=" * 60)
    
    test_results = []
    
    try:
        # 测试API预测功能
        api_success = test_api_prediction()
        test_results.append(("API预测", api_success))
        
        # 测试gRPC服务
        grpc_success = test_grpc_service()
        test_results.append(("gRPC服务", grpc_success))
        
        # 测试错误处理
        test_error_handling()
        test_results.append(("错误处理", True))
        
        # 测试不同数据格式
        test_different_data_formats()
        test_results.append(("数据格式", True))
        
        print("\n" + "=" * 60)
        print("测试结果摘要:")
        for test_name, success in test_results:
            status = "✓ 通过" if success else "✗ 失败"
            print(f"- {test_name}: {status}")
        
        overall_success = all(result[1] for result in test_results)
        print(f"\n整体测试结果: {'✓ 全部通过' if overall_success else '✗ 部分失败'}")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
