#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试路径是否正确
"""

import os
import sys

# 添加时间序列预测模块路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'MyAlgorithm', 'Time-Series-AVC'))

def test_paths():
    """测试各种路径是否正确"""
    
    print("=== 路径测试 ===")
    
    # 当前工作目录
    print(f"当前工作目录: {os.getcwd()}")
    
    # Time-Series-AVC目录
    ts_dir = os.path.join(os.path.dirname(__file__), 'MyAlgorithm', 'Time-Series-AVC')
    print(f"Time-Series-AVC目录: {ts_dir}")
    print(f"Time-Series-AVC目录存在: {os.path.exists(ts_dir)}")
    
    # checkpoints目录
    checkpoints_dir = os.path.join(ts_dir, 'checkpoints')
    print(f"checkpoints目录: {checkpoints_dir}")
    print(f"checkpoints目录存在: {os.path.exists(checkpoints_dir)}")
    
    # 模型路径
    model_name = 'long_term_forecast_Informer  0.002_Informer_custom_ftS_sl28_ll14_pl7_dm64_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_voltage_forecast_0'
    model_dir = os.path.join(checkpoints_dir, model_name)
    model_path = os.path.join(model_dir, 'checkpoint.pth')
    
    print(f"模型目录: {model_dir}")
    print(f"模型目录存在: {os.path.exists(model_dir)}")
    print(f"模型文件: {model_path}")
    print(f"模型文件存在: {os.path.exists(model_path)}")
    
    # dataset目录
    dataset_dir = os.path.join(ts_dir, 'dataset')
    print(f"dataset目录: {dataset_dir}")
    print(f"dataset目录存在: {os.path.exists(dataset_dir)}")
    
    # results目录
    results_dir = os.path.join(ts_dir, 'results')
    print(f"results目录: {results_dir}")
    print(f"results目录存在: {os.path.exists(results_dir)}")
    
    print("\n=== 测试setup_args函数的路径生成 ===")
    
    try:
        from run_for_api import setup_args
        
        args = setup_args('test.csv', 'test_output', 7, 28)
        print(f"args.checkpoints: {args.checkpoints}")
        print(f"args.checkpoints存在: {os.path.exists(args.checkpoints)}")
        print(f"args.root_path: {args.root_path}")
        print(f"args.root_path存在: {os.path.exists(args.root_path)}")
        
        # 测试模型路径构建
        model_path_test = os.path.join(args.checkpoints, model_name, 'checkpoint.pth')
        print(f"构建的模型路径: {model_path_test}")
        print(f"构建的模型路径存在: {os.path.exists(model_path_test)}")
        
    except Exception as e:
        print(f"测试setup_args失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_paths()
