#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试预测输出
"""

import grpc
import json
from services.algorithm_pb2_grpc import AlgorithmStub
from services.algorithm_pb2 import RunOnceParams

def test_prediction_output():
    """测试预测输出数据"""
    
    # 连接到服务器
    channel = grpc.insecure_channel('localhost:50051')
    stub = AlgorithmStub(channel)
    
    # 准备测试数据 - 你的实际数据
    time_series_data = []
    for i in range(31):
        time_series_data.append({
            "time": f"2025-04-{24+i:02d} 00:00:00",
            "value": 111 + i
        })
    
    print("=== 输入数据分析 ===")
    print(f"数据点数量: {len(time_series_data)}")
    print(f"数据范围: {time_series_data[0]['value']} - {time_series_data[-1]['value']}")
    print(f"最后几个数据点:")
    for item in time_series_data[-5:]:
        print(f"  {item['time']}: {item['value']}")
    
    # 创建请求
    request = RunOnceParams(
        equips=[],
        edges=[],
        nodes=[],
        branchs=[],
        datas="",
        options=json.dumps(time_series_data)
    )
    
    print("\n=== 发送预测请求 ===")
    
    # 调用服务
    response = stub.run_once(request)
    
    print(f"\n=== 响应分析 ===")
    print(f"is_success: {response.is_success}")
    print(f"msg: {response.msg}")
    
    if response.is_success and response.data:
        try:
            result = json.loads(response.data)
            
            print(f"\n=== 预测结果分析 ===")
            print(f"预测成功: {result.get('success')}")
            print(f"预测点数量: {len(result.get('predictions', []))}")
            
            if 'predictions' in result:
                predictions = result['predictions']
                print(f"\n预测的未来7天:")
                for i, pred in enumerate(predictions):
                    print(f"  第{i+1}天: {pred['time']} -> {pred['value']:.2f}V")

                # 分析预测趋势
                values = [pred['value'] for pred in predictions]
                print(f"\n预测值范围: {min(values):.2f} - {max(values):.2f}")
                print(f"预测趋势: {'上升' if values[-1] > values[0] else '下降'}")
                
                # 与最后输入值比较
                last_input_value = time_series_data[-1]['value']
                first_pred_value = values[0]
                print(f"最后输入值: {last_input_value}")
                print(f"第一个预测值: {first_pred_value:.2f}")
                print(f"预测变化: {first_pred_value - last_input_value:+.2f}")
            
            if 'model_info' in result:
                model_info = result['model_info']
                print(f"\n=== 模型信息 ===")
                print(f"模型类型: {model_info.get('model_type')}")
                print(f"序列长度: {model_info.get('sequence_length')}")
                print(f"预测长度: {model_info.get('prediction_length')}")
                print(f"输入数据点: {model_info.get('input_data_points')}")
            
            if 'input_summary' in result:
                input_summary = result['input_summary']
                print(f"\n=== 输入数据摘要 ===")
                print(f"开始日期: {input_summary.get('start_date')}")
                print(f"结束日期: {input_summary.get('end_date')}")
                print(f"数据点数: {input_summary.get('data_points')}")
                print(f"电压范围: {input_summary.get('voltage_range')}")
            
        except json.JSONDecodeError as e:
            print(f"JSON解析失败: {e}")
            print(f"原始数据: {response.data[:200]}...")
    else:
        print(f"预测失败: {response.msg}")

if __name__ == "__main__":
    test_prediction_output()
