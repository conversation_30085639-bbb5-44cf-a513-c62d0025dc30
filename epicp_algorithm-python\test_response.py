#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试gRPC响应数据
"""

import grpc
import json
from services.algorithm_pb2_grpc import AlgorithmStub
from services.algorithm_pb2 import RunOnceParams
from services.topologic_pb2 import Equipment, Edge, Node, Branch

def test_response():
    """测试响应数据"""
    
    # 连接到服务器
    channel = grpc.insecure_channel('localhost:50051')
    stub = AlgorithmStub(channel)
    
    # 准备测试数据 - 模拟你的客户端发送的数据
    time_series_data = []
    for i in range(31):
        time_series_data.append({
            "time": f"2025-04-{24+i:02d} 00:00:00",
            "value": 111 + i
        })
    
    # 创建请求 - 模拟你的客户端
    request = RunOnceParams(
        equips=[],
        edges=[],
        nodes=[],
        branchs=[],
        datas="",  # 空的，就像你的客户端
        options=json.dumps(time_series_data)  # 数据在options中，就像你的客户端
    )
    
    print("发送请求...")
    print(f"options长度: {len(request.options)}")
    print(f"datas长度: {len(request.datas)}")
    
    # 调用服务
    response = stub.run_once(request)
    
    print("\n=== 响应分析 ===")
    print(f"is_success: {response.is_success}")
    print(f"msg: {response.msg}")
    print(f"data类型: {type(response.data)}")
    print(f"data长度: {len(response.data)}")
    
    if response.data:
        print(f"\ndata前100字符:")
        print(response.data[:100])
        
        try:
            # 尝试解析JSON
            data_obj = json.loads(response.data)
            print(f"\nJSON解析成功!")
            print(f"success: {data_obj.get('success')}")
            print(f"predictions数量: {len(data_obj.get('predictions', []))}")
            
            if 'predictions' in data_obj:
                predictions = data_obj['predictions']
                print(f"\n前3个预测结果:")
                for i, pred in enumerate(predictions[:3]):
                    print(f"  {i+1}. {pred['date']}: {pred['predicted_voltage']:.2f}")
                    
            print(f"\n完整的JSON数据:")
            print(json.dumps(data_obj, ensure_ascii=False, indent=2))
            
        except json.JSONDecodeError as e:
            print(f"\nJSON解析失败: {e}")
            print(f"原始数据: {response.data}")
    else:
        print("\ndata字段为空!")
    
    print("\n=== 客户端应该这样处理响应 ===")
    print("if (response.is_success) {")
    print("    const result = JSON.parse(response.data);")
    print("    if (result.success) {")
    print("        console.log('预测结果:', result.predictions);")
    print("        // 显示预测数据")
    print("    }")
    print("} else {")
    print("    console.log('错误:', response.msg);")
    print("}")

if __name__ == "__main__":
    test_response()
